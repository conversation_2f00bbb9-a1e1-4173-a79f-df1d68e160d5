
import React, { useState, useCallback } from 'react';
import Navbar from './components/Navbar';
import TabNavigation from './components/TabNavigation';
import MedicationSection from './components/MedicationSection';
import DailyCheckinSection from './components/DailyCheckinSection';
import AppointmentSection from './components/AppointmentSection';
import AiAssistantSection from './components/AiAssistantSection';
import FamilyCoordinationSection from './components/FamilyCoordinationSection';
import { ActiveTab } from './types';
import { DEFAULT_TAB } from './constants';

const App: React.FC = () => {
  const [activeTab, setActiveTab] = useState<ActiveTab>(DEFAULT_TAB);

  const handleTabChange = useCallback((tab: ActiveTab) => {
    setActiveTab(tab);
  }, []);

  const renderActiveSection = () => {
    switch (activeTab) {
      case ActiveTab.Medications:
        return <MedicationSection />;
      case ActiveTab.DailyCheckin:
        return <DailyCheckinSection />;
      case ActiveTab.Appointments:
        return <AppointmentSection />;
      case ActiveTab.AiAssistant:
        return <AiAssistantSection />;
      case ActiveTab.FamilyCoordination:
        return <FamilyCoordinationSection />;
      default:
        return <DailyCheckinSection />;
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Navbar />
      <div className="flex flex-1">
        <TabNavigation activeTab={activeTab} onTabChange={handleTabChange} />
        <main className="flex-1 p-4 sm:p-6 lg:p-8 overflow-y-auto">
          {renderActiveSection()}
        </main>
      </div>
    </div>
  );
};

export default App;
