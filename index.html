
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CareLoop</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#3B82F6', // Blue-500
            secondary: '#10B981', // Emerald-500
            lightgray: '#F3F4F6', // Gray-100
            mediumgray: '#9CA3AF', // Gray-400
            darkgray: '#4B5563', // Gray-600
          },
        }
      }
    }
  </script>
<script type="importmap">
{
  "imports": {
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.2",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "@google/genai": "https://esm.sh/@google/genai"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-lightgray">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>