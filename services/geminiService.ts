
import { GoogleGenAI, GenerateContentResponse, Content } from "@google/genai";
import { ChatMessage } from '../types';
import { AI_ASSISTANT_SYSTEM_INSTRUCTION, GEMINI_TEXT_MODEL } from '../constants';

let ai: GoogleGenAI | null = null;
const IS_CLIENT_CONTEXT = typeof window !== 'undefined';
// In a simple static HTML setup, process.env.API_KEY is typically not available.
// We check for `process` object existence for environments like Node or build tools that might define it.
const API_KEY_AVAILABLE = typeof process !== 'undefined' && process.env && process.env.API_KEY;

if (API_KEY_AVAILABLE) {
  try {
    ai = new GoogleGenAI({ apiKey: process.env.API_KEY });
  } catch (e) {
    console.error("Error initializing GoogleGenAI. Gemini API calls will be mocked.", e);
    ai = null; 
  }
} else {
    if (IS_CLIENT_CONTEXT) { // Only warn in client context if key is missing
        console.warn("API_KEY environment variable not found or 'process' is undefined in this client context. Gemini API calls will be mocked. Ensure API_KEY is set if using a backend or build process.");
    }
    // In a pure server context (not this app's case for this file), this warning might be different.
}

export const geminiService = {
  generateCaregiverAdvice: async (promptText: string, history: ChatMessage[]): Promise<string> => {
    if (!ai) {
      console.warn("Gemini API not initialized (API_KEY might be missing or invalid). Returning mock response.");
      return new Promise(resolve => setTimeout(() => {
        resolve(`This is a mock AI response to: "${promptText}". The Gemini API is not fully configured for this client-side prototype.`);
      }, 500));
    }

    // Convert ChatMessage[] history to Gemini's Content[] format.
    // The `history` array from AiAssistantSection already includes the latest user message.
    const contents: Content[] = history.map(msg => ({
      role: msg.sender === 'user' ? 'user' : 'model',
      parts: [{ text: msg.text }],
    }));

    try {
      const response: GenerateContentResponse = await ai.models.generateContent({
        model: GEMINI_TEXT_MODEL,
        contents: contents,
        config: {
            systemInstruction: AI_ASSISTANT_SYSTEM_INSTRUCTION,
        }
      });
      return response.text;
    } catch (error) {
      console.error('Error generating content with Gemini:', error);
      let detailedErrorMessage = "Failed to get response from AI.";
      if (error instanceof Error) {
        detailedErrorMessage += ` Details: ${error.message}`;
        if (error.message.toLowerCase().includes("api key not valid")) {
            detailedErrorMessage = "The API key is invalid. Please check your configuration.";
        } else if (error.message.toLowerCase().includes("quota")) {
            detailedErrorMessage = "You have exceeded your API quota.";
        } else if (error.message.toLowerCase().includes("model not found")) {
            detailedErrorMessage = `The model ${GEMINI_TEXT_MODEL} was not found. Please check the model name.`;
        }
      }
      return `Sorry, I encountered an error. ${detailedErrorMessage}`;
    }
  },
};
