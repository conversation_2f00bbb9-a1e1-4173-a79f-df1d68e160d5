
export enum ActiveTab {
  DailyCheckin = 'Daily Check-in',
  Medications = 'Medications',
  Appointments = 'Appointments',
  AiAssistant = 'AI Assistant',
  FamilyCoordination = 'Family Coordination',
}

export interface Medication {
  id: string;
  name: string;
  dosage: string;
  time: string;
  status: 'Taken' | 'Missed' | 'Pending';
  photoVerified?: boolean;
}

export interface Appointment {
  id: string;
  doctorName: string;
  specialty: string;
  date: string;
  time: string;
  purpose: string;
  notes?: string;
}

export interface HealthMetric {
  label: string;
  value: string;
  unit?: string;
  trend?: 'up' | 'down' | 'stable';
}

export interface DailyStatus {
  mood: 'Happy' | 'Neutral' | 'Sad' | 'Anxious';
  activity: string;
  notes?: string;
}

export interface FamilyTask {
  id: string;
  description: string;
  assignedTo: string;
  dueDate: string;
  status: 'Pending' | 'In Progress' | 'Completed';
}

export interface ChatMessage {
  id: string;
  sender: 'user' | 'ai';
  text: string;
  timestamp: Date;
}
