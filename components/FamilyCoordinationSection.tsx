import React, { useState, useCallback } from 'react';
import { FamilyTask } from '../types';
import Card from './shared/Card';
import Button from './shared/Button';
import { UsersIcon } from './icons/UsersIcon';
import { PlusIcon } from './icons/PlusIcon';
import { CheckCircleIcon } from './icons/CheckCircleIcon';
import { ClockIcon } from './icons/ClockIcon'; 
import { RefreshIcon } from './icons/RefreshIcon'; // Placeholder for In Progress

const initialTasks: FamilyTask[] = [
  { id: '1', description: 'Pick up prescription for Mom', assignedTo: '<PERSON> (Daughter)', dueDate: '2024-07-10', status: 'Pending' },
  { id: '2', description: 'Take <PERSON> to doctor appointment', assignedTo: '<PERSON> (Son)', dueDate: '2024-07-12', status: 'In Progress' },
  { id: '3', description: 'Grocery shopping for parents', assignedTo: '<PERSON> (Daughter)', dueDate: '2024-07-08', status: 'Completed' },
];

// Mock Modal Component
const MockModal: React.FC<{ title: string; isOpen: boolean; onClose: () => void; children: React.ReactNode }> = ({ title, isOpen, onClose, children }) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-darkgray">{title}</h3>
          <Button onClick={onClose} variant="ghost" size="sm">Close</Button>
        </div>
        {children}
      </div>
    </div>
  );
};

interface TaskItemProps {
  task: FamilyTask;
  onToggleStatus: (id: string) => void;
}

const TaskItem: React.FC<TaskItemProps> = ({ task, onToggleStatus }) => {
  const getStatusStyles = (status: FamilyTask['status']): { color: string; icon: React.ReactNode } => {
    switch (status) {
      case 'Completed':
        return { color: 'text-green-700 bg-green-100', icon: <CheckCircleIcon className="h-4 w-4 mr-1" /> };
      case 'In Progress':
        return { color: 'text-yellow-700 bg-yellow-100', icon: <RefreshIcon className="h-4 w-4 mr-1 animate-spin-slow" /> }; // animate-spin-slow can be defined in global css if needed, or use a static icon.
      case 'Pending':
      default:
        return { color: 'text-blue-700 bg-blue-100', icon: <ClockIcon className="h-4 w-4 mr-1" /> };
    }
  };

  const statusStyles = getStatusStyles(task.status);

  return (
    <Card className="mb-4 p-4 hover:shadow-md transition-shadow">
      <div className="flex flex-col sm:flex-row justify-between items-start">
        <div className="flex-grow mb-2 sm:mb-0">
          <h4 className="text-md font-semibold text-primary">{task.description}</h4>
          <p className="text-sm text-mediumgray">Assigned to: <span className="font-medium text-darkgray">{task.assignedTo}</span></p>
          <p className="text-sm text-mediumgray">Due: <span className="font-medium text-darkgray">{new Date(task.dueDate).toLocaleDateString()}</span></p>
        </div>
        <div className="flex flex-col items-start sm:items-end space-y-2">
          <span className={`px-3 py-1 text-xs font-semibold rounded-full flex items-center ${statusStyles.color}`}>
            {statusStyles.icon}
            {task.status}
          </span>
          {task.status !== 'Completed' && (
            <Button 
              onClick={() => onToggleStatus(task.id)} 
              variant="outline" 
              size="sm"
              className="text-xs"
            >
              {task.status === 'Pending' ? 'Start Task' : 'Mark as Completed'}
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
};


const FamilyCoordinationSection: React.FC = () => {
  const [tasks, setTasks] = useState<FamilyTask[]>(initialTasks);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formState, setFormState] = useState<Partial<Omit<FamilyTask, 'id' | 'status'>>>({
    description: '',
    assignedTo: '',
    dueDate: new Date().toISOString().split('T')[0] // Default to today
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormState(prev => ({ ...prev, [name]: value }));
  };

  const handleAddTask = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (!formState.description || !formState.assignedTo || !formState.dueDate) {
      alert("Please fill all fields.");
      return;
    }
    const newTask: FamilyTask = {
      id: String(Date.now()),
      description: formState.description,
      assignedTo: formState.assignedTo,
      dueDate: formState.dueDate,
      status: 'Pending',
    };
    setTasks(prev => [newTask, ...prev].sort((a,b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()));
    setFormState({ description: '', assignedTo: '', dueDate: new Date().toISOString().split('T')[0] });
    setIsModalOpen(false);
  }, [formState]);

  const handleToggleStatus = useCallback((id: string) => {
    setTasks(prevTasks =>
      prevTasks.map(task => {
        if (task.id === id) {
          if (task.status === 'Pending') return { ...task, status: 'In Progress' };
          if (task.status === 'In Progress') return { ...task, status: 'Completed' };
        }
        return task;
      })
    );
  }, []);
  
  const inputBaseClasses = "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm";

  const pendingTasks = tasks.filter(t => t.status === 'Pending');
  const inProgressTasks = tasks.filter(t => t.status === 'In Progress');
  const completedTasks = tasks.filter(t => t.status === 'Completed');

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-darkgray flex items-center">
          <UsersIcon className="h-7 w-7 mr-2 text-primary" /> Family Coordination
        </h2>
        <Button onClick={() => setIsModalOpen(true)} className="flex items-center">
          <PlusIcon className="h-5 w-5 mr-1" /> Add Task
        </Button>
      </div>

      <MockModal title="Add New Family Task" isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <form onSubmit={handleAddTask} className="space-y-4">
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">Task Description</label>
            <input type="text" name="description" id="description" value={formState.description || ''} onChange={handleInputChange} className={inputBaseClasses} required />
          </div>
          <div>
            <label htmlFor="assignedTo" className="block text-sm font-medium text-gray-700">Assigned To</label>
            <input type="text" name="assignedTo" id="assignedTo" value={formState.assignedTo || ''} onChange={handleInputChange} className={inputBaseClasses} placeholder="e.g., John D." required />
          </div>
          <div>
            <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700">Due Date</label>
            <input type="date" name="dueDate" id="dueDate" value={formState.dueDate || ''} onChange={handleInputChange} className={inputBaseClasses} required />
          </div>
          <div className="flex justify-end space-x-2 pt-2">
            <Button type="button" variant="outline" onClick={() => { setIsModalOpen(false); setFormState({ description: '', assignedTo: '', dueDate: new Date().toISOString().split('T')[0] }); }}>Cancel</Button>
            <Button type="submit">Add Task</Button>
          </div>
        </form>
      </MockModal>

      <div>
        <h3 className="text-xl font-medium text-darkgray mb-3">Pending Tasks</h3>
        {pendingTasks.length > 0 ? (
          pendingTasks.map(task => <TaskItem key={task.id} task={task} onToggleStatus={handleToggleStatus} />)
        ) : (
          <Card><p className="text-mediumgray p-4 text-center">No pending tasks.</p></Card>
        )}
      </div>

      <div>
        <h3 className="text-xl font-medium text-darkgray mb-3">In Progress Tasks</h3>
        {inProgressTasks.length > 0 ? (
          inProgressTasks.map(task => <TaskItem key={task.id} task={task} onToggleStatus={handleToggleStatus} />)
        ) : (
          <Card><p className="text-mediumgray p-4 text-center">No tasks currently in progress.</p></Card>
        )}
      </div>
      
      <div>
        <h3 className="text-xl font-medium text-darkgray mb-3">Completed Tasks</h3>
        {completedTasks.length > 0 ? (
          completedTasks.map(task => <TaskItem key={task.id} task={task} onToggleStatus={handleToggleStatus} />)
        ) : (
          <Card><p className="text-mediumgray p-4 text-center">No tasks completed yet.</p></Card>
        )}
      </div>
       <Card>
        <div className="p-4">
          <h4 className="text-md font-semibold text-darkgray mb-2">Note:</h4>
          <p className="text-sm text-mediumgray">
            This section helps coordinate tasks among family members. Real-time updates and notifications would require a backend and user authentication system.
          </p>
        </div>
      </Card>
    </div>
  );
};

export default FamilyCoordinationSection;