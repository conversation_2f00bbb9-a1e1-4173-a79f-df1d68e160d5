
import React, { useState, useCallback } from 'react';
import { Medication } from '../types';
import Card from './shared/Card';
import Button from './shared/Button';
import { PillIcon } from './icons/PillIcon';
import { CheckCircleIcon } from './icons/CheckCircleIcon';
import { XCircleIcon } from './icons/XCircleIcon';
import { PlusIcon } from './icons/PlusIcon';
import { CameraIcon } from './icons/CameraIcon'; // Assuming you have CameraIcon

const initialMedications: Medication[] = [
  { id: '1', name: 'Lisinopril', dosage: '10mg', time: '8:00 AM', status: 'Pending' },
  { id: '2', name: 'Metformin', dosage: '500mg', time: '8:00 AM', status: 'Taken', photoVerified: true },
  { id: '3', name: 'Atorvastatin', dosage: '20mg', time: '8:00 PM', status: 'Pending' },
  { id: '4', name: '<PERSON><PERSON><PERSON><PERSON>', dosage: '5mg', time: '8:00 AM', status: 'Missed' },
];

// Mock Modal Component - in a real app, this would be more robust
const MockModal: React.FC<{ title: string; isOpen: boolean; onClose: () => void; children: React.ReactNode }> = ({ title, isOpen, onClose, children }) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-darkgray">{title}</h3>
          <Button onClick={onClose} variant="ghost" size="sm">Close</Button>
        </div>
        {children}
      </div>
    </div>
  );
};


const MedicationItem: React.FC<{ medication: Medication; onToggleStatus: (id: string, status: Medication['status']) => void; onVerifyPhoto: (id: string) => void; }> = ({ medication, onToggleStatus, onVerifyPhoto }) => {
  const handleStatusChange = (newStatus: Medication['status']) => {
    onToggleStatus(medication.id, newStatus);
  };

  return (
    <Card className="mb-4 p-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div className="mb-2 sm:mb-0">
          <h3 className="text-lg font-semibold text-primary">{medication.name}</h3>
          <p className="text-sm text-darkgray">{medication.dosage} - {medication.time}</p>
        </div>
        <div className="flex items-center space-x-2">
           {medication.status === 'Pending' && (
            <Button onClick={() => handleStatusChange('Taken')} variant="success" size="sm">Mark Taken</Button>
          )}
           {medication.status === 'Taken' && (
            <span className="flex items-center text-green-600">
              <CheckCircleIcon className="h-5 w-5 mr-1" /> Taken
              {medication.photoVerified && (
                <span title="Photo Verified">
                  <CameraIcon className="h-4 w-4 ml-1 text-blue-500" />
                </span>
              )}
            </span>
          )}
           {medication.status === 'Missed' && (
            <span className="flex items-center text-red-600">
              <XCircleIcon className="h-5 w-5 mr-1" /> Missed
            </span>
          )}
          {!medication.photoVerified && medication.status === 'Taken' && (
             <Button onClick={() => onVerifyPhoto(medication.id)} variant="outline" size="sm" className="flex items-center">
                <CameraIcon className="h-4 w-4 mr-1" /> Verify
             </Button>
          )}
        </div>
      </div>
      {/* Simple photo upload simulation */}
      {medication.status === 'Taken' && !medication.photoVerified && (
        <div className="mt-2 text-xs text-gray-500">
          Photo verification pending. In a real app, integrate camera/upload here.
        </div>
      )}
    </Card>
  );
};


const MedicationSection: React.FC = () => {
  const [medications, setMedications] = useState<Medication[]>(initialMedications);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newMedName, setNewMedName] = useState('');
  const [newMedDosage, setNewMedDosage] = useState('');
  const [newMedTime, setNewMedTime] = useState('');


  const handleToggleStatus = useCallback((id: string, status: Medication['status']) => {
    setMedications(prevMeds =>
      prevMeds.map(med => (med.id === id ? { ...med, status, photoVerified: status === 'Taken' ? med.photoVerified : undefined } : med))
    );
  }, []);

  const handleVerifyPhoto = useCallback((id: string) => {
    // Simulate photo verification
    setMedications(prevMeds =>
      prevMeds.map(med => (med.id === id ? { ...med, photoVerified: true } : med))
    );
    alert(`Photo verified for medication ID: ${id}. (This is a mock action)`);
  }, []);

  const handleAddMedication = (e: React.FormEvent) => {
    e.preventDefault();
    if(!newMedName || !newMedDosage || !newMedTime) {
      alert("Please fill all fields for the new medication.");
      return;
    }
    const newMed: Medication = {
        id: String(Date.now()),
        name: newMedName,
        dosage: newMedDosage,
        time: newMedTime,
        status: 'Pending'
    };
    setMedications(prev => [newMed, ...prev]);
    setNewMedName('');
    setNewMedDosage('');
    setNewMedTime('');
    setIsModalOpen(false);
  };


  const upcomingMedications = medications.filter(m => m.status === 'Pending').sort((a,b) => a.time.localeCompare(b.time));
  const takenMedications = medications.filter(m => m.status === 'Taken');
  const missedMedications = medications.filter(m => m.status === 'Missed');

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-darkgray flex items-center">
          <PillIcon className="h-7 w-7 mr-2 text-primary" /> Medication Schedule
        </h2>
        <Button onClick={() => setIsModalOpen(true)} className="flex items-center">
          <PlusIcon className="h-5 w-5 mr-1" /> Add Medication
        </Button>
      </div>

      <MockModal title="Add New Medication" isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <form onSubmit={handleAddMedication} className="space-y-4">
            <div>
                <label htmlFor="medName" className="block text-sm font-medium text-gray-700">Name</label>
                <input type="text" id="medName" value={newMedName} onChange={e => setNewMedName(e.target.value)} className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" required />
            </div>
            <div>
                <label htmlFor="medDosage" className="block text-sm font-medium text-gray-700">Dosage</label>
                <input type="text" id="medDosage" value={newMedDosage} onChange={e => setNewMedDosage(e.target.value)} className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" required />
            </div>
            <div>
                <label htmlFor="medTime" className="block text-sm font-medium text-gray-700">Time (e.g., 8:00 AM)</label>
                <input type="text" id="medTime" value={newMedTime} onChange={e => setNewMedTime(e.target.value)} className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" required />
            </div>
            <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)}>Cancel</Button>
                <Button type="submit">Add Medication</Button>
            </div>
        </form>
      </MockModal>

      <div>
        <h3 className="text-xl font-medium text-darkgray mb-3">Upcoming</h3>
        {upcomingMedications.length > 0 ? (
          upcomingMedications.map(med => <MedicationItem key={med.id} medication={med} onToggleStatus={handleToggleStatus} onVerifyPhoto={handleVerifyPhoto} />)
        ) : (
          <Card><p className="text-mediumgray p-4">No upcoming medications.</p></Card>
        )}
      </div>

      <div>
        <h3 className="text-xl font-medium text-darkgray mb-3">Adherence History</h3>
        {takenMedications.length > 0 && (
          <div className="mb-4">
            <h4 className="text-lg font-normal text-green-600 mb-2">Taken</h4>
            {takenMedications.map(med => <MedicationItem key={med.id} medication={med} onToggleStatus={handleToggleStatus} onVerifyPhoto={handleVerifyPhoto} />)}
          </div>
        )}
        {missedMedications.length > 0 && (
           <div>
            <h4 className="text-lg font-normal text-red-600 mb-2">Missed</h4>
            {missedMedications.map(med => <MedicationItem key={med.id} medication={med} onToggleStatus={handleToggleStatus} onVerifyPhoto={handleVerifyPhoto} />)}
          </div>
        )}
        {takenMedications.length === 0 && missedMedications.length === 0 && (
            <Card><p className="text-mediumgray p-4">No medication history yet.</p></Card>
        )}
      </div>
       <Card>
        <div className="p-4">
          <h4 className="text-md font-semibold text-darkgray mb-2">Note:</h4>
          <p className="text-sm text-mediumgray">
            This is a UI mockup. Medication reminders (SMS, email, push), smart pill dispenser integration, and real photo verification would require backend implementation and external service integrations.
          </p>
        </div>
      </Card>
    </div>
  );
};

export default MedicationSection;
