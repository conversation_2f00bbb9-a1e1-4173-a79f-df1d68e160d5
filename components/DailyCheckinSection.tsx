
import React, { useState } from 'react';
import { DailyStatus, HealthMetric } from '../types';
import Card from './shared/Card';
import Button from './shared/Button';
import { HomeIcon } from './icons/HomeIcon';
import { SunIcon } from './icons/SunIcon'; // Placeholder
import { TrendingUpIcon } from './icons/TrendingUpIcon'; // Placeholder
import { TrendingDownIcon } from './icons/TrendingDownIcon'; // Placeholder
import { MinusSmIcon } from './icons/MinusSmIcon'; // Placeholder for stable trend


const initialDailyStatus: DailyStatus = {
  mood: 'Happy',
  activity: 'Went for a short walk in the garden. Read a book.',
  notes: 'Feeling generally well today.',
};

const initialHealthMetrics: HealthMetric[] = [
  { label: 'Blood Pressure', value: '125/80', unit: 'mmHg', trend: 'stable' },
  { label: 'Heart Rate', value: '70', unit: 'bpm', trend: 'stable' },
  { label: 'Sleep', value: '7.5', unit: 'hours', trend: 'up' },
  { label: 'Steps', value: '3,500', trend: 'down' },
];

const MoodSelector: React.FC<{ currentMood: DailyStatus['mood']; onMoodChange: (mood: DailyStatus['mood']) => void }> = ({ currentMood, onMoodChange }) => {
  const moods: DailyStatus['mood'][] = ['Happy', 'Neutral', 'Sad', 'Anxious'];
  // Simple emoji representation, ideally use icons
  const moodIcons: Record<DailyStatus['mood'], string> = {
    'Happy': '😊',
    'Neutral': '😐',
    'Sad': '😟',
    'Anxious': '😥'
  };

  return (
    <div className="flex space-x-2">
      {moods.map(mood => (
        <button
          key={mood}
          onClick={() => onMoodChange(mood)}
          className={`p-2 rounded-lg text-2xl ${currentMood === mood ? 'bg-blue-100 ring-2 ring-primary' : 'hover:bg-gray-100'}`}
          title={mood}
        >
          {moodIcons[mood]}
        </button>
      ))}
    </div>
  );
};

const TrendIcon: React.FC<{ trend?: 'up' | 'down' | 'stable' }> = ({ trend }) => {
  if (trend === 'up') return <TrendingUpIcon className="h-5 w-5 text-green-500" />;
  if (trend === 'down') return <TrendingDownIcon className="h-5 w-5 text-red-500" />;
  if (trend === 'stable') return <MinusSmIcon className="h-5 w-5 text-gray-500" />; // Using MinusSmIcon as placeholder
  return null;
};


const DailyCheckinSection: React.FC = () => {
  const [dailyStatus, setDailyStatus] = useState<DailyStatus>(initialDailyStatus);
  const [healthMetrics, setHealthMetrics] = useState<HealthMetric[]>(initialHealthMetrics); // In real app, this would be fetched or updated

  const handleStatusUpdate = () => {
    // This would typically involve a backend call
    alert(`Status updated (mock): Mood - ${dailyStatus.mood}, Activity - ${dailyStatus.activity}`);
  };

  const handleMoodChange = (newMood: DailyStatus['mood']) => {
    setDailyStatus(prev => ({ ...prev, mood: newMood }));
  };
  
  const handleActivityChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDailyStatus(prev => ({...prev, activity: e.target.value}));
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-darkgray flex items-center">
        <HomeIcon className="h-7 w-7 mr-2 text-primary" /> Daily Check-in for Mom
      </h2>

      <Card>
        <div className="p-6">
          <h3 className="text-xl font-semibold text-darkgray mb-4">How is Mom feeling today?</h3>
          <div className="mb-4">
             <label className="block text-sm font-medium text-gray-700 mb-1">Mood</label>
             <MoodSelector currentMood={dailyStatus.mood} onMoodChange={handleMoodChange} />
          </div>
          <div className="mb-4">
            <label htmlFor="activity" className="block text-sm font-medium text-gray-700">Activities & Notes</label>
            <textarea
              id="activity"
              rows={3}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              value={dailyStatus.activity}
              onChange={handleActivityChange}
              placeholder="e.g., Watched a movie, had a good chat."
            />
          </div>
          <Button onClick={handleStatusUpdate} className="w-full sm:w-auto">
            Log Today's Update
          </Button>
        </div>
      </Card>

      <Card>
        <div className="p-6">
          <h3 className="text-xl font-semibold text-darkgray mb-4 flex items-center">
            <SunIcon className="h-6 w-6 mr-2 text-secondary" /> Current Status Overview
          </h3>
          <div className="space-y-3">
            <p><strong className="text-mediumgray">Mood:</strong> <span className="font-medium">{dailyStatus.mood}</span></p>
            <p><strong className="text-mediumgray">Recent Activity:</strong> <span className="font-medium">{dailyStatus.activity || "No activity logged."}</span></p>
            {dailyStatus.notes && <p><strong className="text-mediumgray">Notes:</strong> <span className="font-medium">{dailyStatus.notes}</span></p>}
          </div>
        </div>
      </Card>

      <Card>
        <div className="p-6">
          <h3 className="text-xl font-semibold text-darkgray mb-4">Health Metrics</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {healthMetrics.map(metric => (
              <div key={metric.label} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-md font-medium text-darkgray">{metric.label}</h4>
                  {metric.trend && <TrendIcon trend={metric.trend} />}
                </div>
                <p className="text-2xl font-bold text-primary">{metric.value} <span className="text-sm font-normal text-mediumgray">{metric.unit}</span></p>
              </div>
            ))}
          </div>
           <p className="text-xs text-mediumgray mt-4">
            Note: Health metrics are illustrative. Real integration with wearable devices (Fitbit, Apple Watch) requires API access and backend processing.
          </p>
        </div>
      </Card>
    </div>
  );
};

export default DailyCheckinSection;
