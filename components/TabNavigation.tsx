
import React from 'react';
import { ActiveTab } from '../types';
import { HomeIcon } from './icons/HomeIcon';
import { PillIcon } from './icons/PillIcon';
import { CalendarIcon } from './icons/CalendarIcon';
import { ChatIcon } from './icons/ChatIcon';
import { UsersIcon } from './icons/UsersIcon';

interface TabNavigationProps {
  activeTab: ActiveTab;
  onTabChange: (tab: ActiveTab) => void;
}

interface NavItemProps {
  label: ActiveTab;
  icon: React.ReactNode;
  isActive: boolean;
  onClick: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ label, icon, isActive, onClick }) => (
  <button
    onClick={onClick}
    className={`flex items-center w-full px-4 py-3 text-sm font-medium rounded-md transition-colors duration-150 ease-in-out
                ${isActive ? 'bg-primary text-white shadow-lg' : 'text-darkgray hover:bg-gray-200 hover:text-primary'}`}
  >
    {icon}
    <span className="ml-3">{label}</span>
  </button>
);

const TabNavigation: React.FC<TabNavigationProps> = ({ activeTab, onTabChange }) => {
  const navItems = [
    { label: ActiveTab.DailyCheckin, icon: <HomeIcon className="h-5 w-5" /> },
    { label: ActiveTab.Medications, icon: <PillIcon className="h-5 w-5" /> },
    { label: ActiveTab.Appointments, icon: <CalendarIcon className="h-5 w-5" /> },
    { label: ActiveTab.AiAssistant, icon: <ChatIcon className="h-5 w-5" /> },
    { label: ActiveTab.FamilyCoordination, icon: <UsersIcon className="h-5 w-5" /> },
  ];

  return (
    <aside className="w-64 bg-white p-4 space-y-2 border-r border-gray-200 shadow-sm flex-shrink-0">
      <nav className="space-y-1">
        {navItems.map((item) => (
          <NavItem
            key={item.label}
            label={item.label}
            icon={item.icon}
            isActive={activeTab === item.label}
            onClick={() => onTabChange(item.label)}
          />
        ))}
      </nav>
    </aside>
  );
};

export default TabNavigation;
