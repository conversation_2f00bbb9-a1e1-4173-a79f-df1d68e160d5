
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { ChatMessage } from '../types';
import { geminiService } from '../services/geminiService';
import Card from './shared/Card';
import Button from './shared/Button';
import Input from './shared/Input';
import LoadingSpinner from './shared/LoadingSpinner';
import { ChatIcon } from './icons/ChatIcon';
import { SendIcon } from './icons/SendIcon';
import { AI_ASSISTANT_PRE_WRITTEN_PROMPTS } from '../constants';

const AiAssistantSection: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [userInput, setUserInput] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Scroll to bottom of chat on new message
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSendMessage = useCallback(async (messageText?: string) => {
    const textToSend = messageText || userInput;
    if (!textToSend.trim()) return;

    const newUserMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: 'user',
      text: textToSend,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, newUserMessage]);
    setUserInput('');
    setIsLoading(true);
    setError(null);

    try {
      const aiResponseText = await geminiService.generateCaregiverAdvice(textToSend, messages);
      const newAiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        sender: 'ai',
        text: aiResponseText,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, newAiMessage]);
    } catch (err) {
      console.error("Error getting AI response:", err);
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred.";
      setError(`Failed to get response from AI assistant: ${errorMessage}. Ensure your API key is configured.`);
       const newErrorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        sender: 'ai',
        text: `Sorry, I encountered an error: ${errorMessage}. Please try again later or check your API key setup.`,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, newErrorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [userInput, messages]);

  const handlePromptClick = (prompt: string) => {
    setUserInput(prompt);
    // Optionally, send message immediately:
    // handleSendMessage(prompt); 
  };

  return (
    <div className="flex flex-col h-[calc(100vh-10rem)]"> {/* Adjust height as needed */}
      <h2 className="text-2xl font-semibold text-darkgray mb-4 flex items-center">
        <ChatIcon className="h-7 w-7 mr-2 text-primary" /> AI Conversation Assistant
      </h2>
      
      <Card className="mb-4 p-4">
        <h3 className="text-lg font-semibold text-darkgray mb-2">Need help with a tough conversation?</h3>
        <p className="text-sm text-mediumgray mb-3">
          Select a common topic below, or type your own question to get advice from CareBot.
        </p>
        <div className="flex flex-wrap gap-2">
          {AI_ASSISTANT_PRE_WRITTEN_PROMPTS.map(prompt => (
            <Button 
              key={prompt.title} 
              variant="outline" 
              size="sm"
              onClick={() => handlePromptClick(prompt.prompt)}
              className="text-xs sm:text-sm"
            >
              {prompt.title}
            </Button>
          ))}
        </div>
      </Card>

      <Card className="flex-grow flex flex-col overflow-hidden">
        <div ref={chatContainerRef} className="flex-grow p-4 space-y-4 overflow-y-auto bg-gray-50 rounded-t-md">
          {messages.map(msg => (
            <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg shadow
                            ${msg.sender === 'user' ? 'bg-primary text-white' : 'bg-white text-darkgray border border-gray-200'}`}
              >
                <p className="text-sm whitespace-pre-wrap">{msg.text}</p>
                <p className={`text-xs mt-1 ${msg.sender === 'user' ? 'text-blue-200' : 'text-mediumgray'} text-right`}>
                  {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex justify-start">
              <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg shadow bg-white text-darkgray border border-gray-200">
                <LoadingSpinner size="sm" />
              </div>
            </div>
          )}
        </div>
        {error && <p className="p-4 text-sm text-red-600 bg-red-50 border-t border-red-200">{error}</p>}
        <div className="p-4 border-t border-gray-200 bg-white rounded-b-md">
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleSendMessage();
            }}
            className="flex items-center space-x-2"
          >
            <Input
              type="text"
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              placeholder="Ask for advice or type your concern..."
              className="flex-grow"
              disabled={isLoading}
            />
            <Button type="submit" disabled={isLoading || !userInput.trim()} className="px-3 py-2">
              {isLoading ? <LoadingSpinner size="sm" /> : <SendIcon className="h-5 w-5" />}
            </Button>
          </form>
        </div>
      </Card>
    </div>
  );
};

export default AiAssistantSection;
