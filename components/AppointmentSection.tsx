import React, { useState, useCallback } from 'react';
import { Appointment } from '../types';
import Card from './shared/Card';
import Button from './shared/Button';
import { CalendarIcon } from './icons/CalendarIcon';
import { PlusIcon } from './icons/PlusIcon';

const initialAppointments: Appointment[] = [
  { id: '1', doctorName: 'Dr. <PERSON>', specialty: 'Cardiologist', date: '2024-07-15', time: '10:00 AM', purpose: 'Annual Checkup', notes: 'Bring recent EKG printout.' },
  { id: '2', doctorName: 'Dr. <PERSON>', specialty: 'Ophthalmologist', date: '2024-07-22', time: '2:30 PM', purpose: 'Eye Exam' },
  { id: '3', doctorName: 'Dr. <PERSON>', specialty: 'General Practitioner', date: '2024-08-05', time: '11:00 AM', purpose: 'Follow-up on blood tests' },
];

// Mock Modal Component
const MockModal: React.FC<{ title: string; isOpen: boolean; onClose: () => void; children: React.ReactNode }> = ({ title, isOpen, onClose, children }) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-darkgray">{title}</h3>
          <Button onClick={onClose} variant="ghost" size="sm">Close</Button>
        </div>
        {children}
      </div>
    </div>
  );
};

const AppointmentItem: React.FC<{ appointment: Appointment }> = ({ appointment }) => {
  const appointmentDate = new Date(appointment.date + 'T' + appointment.time.replace(' AM', ':00').replace(' PM', ':00 PM')); // Basic time parsing
  return (
    <Card className="mb-4 p-4">
      <h3 className="text-lg font-semibold text-primary">{appointment.doctorName} - <span className="font-normal text-darkgray">{appointment.specialty}</span></h3>
      <p className="text-sm text-darkgray">{new Intl.DateTimeFormat('en-US', { dateStyle: 'full' }).format(appointmentDate)} at {appointment.time}</p>
      <p className="text-sm text-mediumgray mt-1"><strong>Purpose:</strong> {appointment.purpose}</p>
      {appointment.notes && <p className="text-xs text-mediumgray mt-1 bg-yellow-50 p-2 rounded"><strong>Notes:</strong> {appointment.notes}</p>}
      <div className="mt-3">
        <Button variant="outline" size="sm" onClick={() => alert(`Preparation checklist for ${appointment.doctorName}. (Mock content)`)}>
          Preparation Checklist
        </Button>
      </div>
    </Card>
  );
};

const AppointmentSection: React.FC = () => {
  const [appointments, setAppointments] = useState<Appointment[]>(initialAppointments);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formState, setFormState] = useState<Partial<Appointment>>({});


  const handleAddAppointment = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (!formState.doctorName || !formState.specialty || !formState.date || !formState.time || !formState.purpose) {
        alert("Please fill all required fields.");
        return;
    }
    const newAppointment: Appointment = {
        id: String(Date.now()),
        doctorName: formState.doctorName,
        specialty: formState.specialty,
        date: formState.date,
        time: formState.time,
        purpose: formState.purpose,
        notes: formState.notes,
    };
    setAppointments(prev => [newAppointment, ...prev].sort((a,b) => new Date(a.date + 'T' + a.time).getTime() - new Date(b.date + 'T' + b.time).getTime()));
    setFormState({});
    setIsModalOpen(false);
  }, [formState]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormState(prev => ({...prev, [name]: value}));
  };

  const upcomingAppointments = appointments.filter(apt => new Date(apt.date + 'T' + apt.time) >= new Date()).sort((a,b) => new Date(a.date + 'T' + a.time).getTime() - new Date(b.date + 'T' + b.time).getTime());
  const pastAppointments = appointments.filter(apt => new Date(apt.date + 'T' + apt.time) < new Date()).sort((a,b) => new Date(b.date + 'T' + b.time).getTime() - new Date(a.date + 'T' + a.time).getTime());

  const inputBaseClasses = "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm";

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-darkgray flex items-center">
          <CalendarIcon className="h-7 w-7 mr-2 text-primary" /> Appointment Management
        </h2>
        <Button onClick={() => setIsModalOpen(true)} className="flex items-center">
          <PlusIcon className="h-5 w-5 mr-1" /> Add Appointment
        </Button>
      </div>

       <MockModal title="Add New Appointment" isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <form onSubmit={handleAddAppointment} className="space-y-4">
            <div>
                <label htmlFor="doctorName" className="block text-sm font-medium text-gray-700">Doctor's Name</label>
                <input type="text" name="doctorName" id="doctorName" value={formState.doctorName || ''} onChange={handleInputChange} className={inputBaseClasses} required />
            </div>
            <div>
                <label htmlFor="specialty" className="block text-sm font-medium text-gray-700">Specialty</label>
                <input type="text" name="specialty" id="specialty" value={formState.specialty || ''} onChange={handleInputChange} className={inputBaseClasses} required />
            </div>
            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label htmlFor="date" className="block text-sm font-medium text-gray-700">Date</label>
                    <input type="date" name="date" id="date" value={formState.date || ''} onChange={handleInputChange} className={inputBaseClasses} required />
                </div>
                <div>
                    <label htmlFor="time" className="block text-sm font-medium text-gray-700">Time</label>
                    <input type="time" name="time" id="time" value={formState.time || ''} onChange={handleInputChange} className={inputBaseClasses} required />
                </div>
            </div>
            <div>
                <label htmlFor="purpose" className="block text-sm font-medium text-gray-700">Purpose</label>
                <input type="text" name="purpose" id="purpose" value={formState.purpose || ''} onChange={handleInputChange} className={inputBaseClasses} required />
            </div>
             <div>
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700">Notes (Optional)</label>
                <textarea name="notes" id="notes" value={formState.notes || ''} onChange={handleInputChange} rows={2} className={inputBaseClasses}></textarea>
            </div>
            <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => { setIsModalOpen(false); setFormState({}); }}>Cancel</Button>
                <Button type="submit">Add Appointment</Button>
            </div>
        </form>
      </MockModal>

      <div>
        <h3 className="text-xl font-medium text-darkgray mb-3">Upcoming Appointments</h3>
        {upcomingAppointments.length > 0 ? (
          upcomingAppointments.map(apt => <AppointmentItem key={apt.id} appointment={apt} />)
        ) : (
          <Card><p className="text-mediumgray p-4">No upcoming appointments.</p></Card>
        )}
      </div>

      <div>
        <h3 className="text-xl font-medium text-darkgray mb-3">Past Appointments</h3>
         {pastAppointments.length > 0 ? (
          pastAppointments.map(apt => <AppointmentItem key={apt.id} appointment={apt} />)
        ) : (
          <Card><p className="text-mediumgray p-4">No past appointments recorded.</p></Card>
        )}
      </div>
      <Card>
        <div className="p-4">
          <h4 className="text-md font-semibold text-darkgray mb-2">Note:</h4>
          <p className="text-sm text-mediumgray">
            This is a UI mockup. Calendar integration (Google Calendar, Outlook) and automated reminders require backend implementation and API integrations.
          </p>
        </div>
      </Card>
    </div>
  );
};

export default AppointmentSection;