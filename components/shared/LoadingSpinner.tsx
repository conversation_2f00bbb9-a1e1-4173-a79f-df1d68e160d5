
import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  color?: string; // e.g., 'border-white', 'border-primary'
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = 'md', className, color = 'border-primary' }) => {
  let sizeClasses = '';
  switch (size) {
    case 'sm':
      sizeClasses = 'h-5 w-5 border-2'; // Thinner border for smaller spinner
      break;
    case 'md':
      sizeClasses = 'h-8 w-8 border-4';
      break;
    case 'lg':
      sizeClasses = 'h-12 w-12 border-4';
      break;
  }

  return (
    <div className={`flex items-center justify-center ${className || ''}`}>
      <div
        className={`${sizeClasses} ${color} border-t-transparent animate-spin rounded-full`}
        role="status"
        aria-live="polite"
        aria-label="Loading"
      >
        <span className="sr-only">Loading...</span>
      </div>
    </div>
  );
};

export default LoadingSpinner;
