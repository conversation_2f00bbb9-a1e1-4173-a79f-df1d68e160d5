
import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark' | 'outline' | 'ghost';
  size?: 'xs' | 'sm' | 'md' | 'lg';
}

const Button: React.FC<ButtonProps> = ({ children, className, variant = 'primary', size = 'md', ...props }) => {
  const baseStyles = "font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition ease-in-out duration-150 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center";
  
  let variantStyles = "";
  switch (variant) {
    case 'primary':
      variantStyles = 'bg-primary text-white hover:bg-blue-600 focus:ring-primary';
      break;
    case 'secondary':
      variantStyles = 'bg-secondary text-white hover:bg-emerald-600 focus:ring-secondary';
      break;
    case 'success':
      variantStyles = 'bg-green-500 text-white hover:bg-green-600 focus:ring-green-500';
      break;
    case 'danger':
      variantStyles = 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500';
      break;
    case 'outline':
      variantStyles = 'border border-primary text-primary hover:bg-blue-50 focus:ring-primary';
      break;
    case 'ghost':
      variantStyles = 'text-primary hover:bg-blue-50 focus:ring-primary';
      break;
    default: // Fallback to primary
      variantStyles = 'bg-primary text-white hover:bg-blue-600 focus:ring-primary';
  }

  let sizeStyles = "";
  switch (size) {
    case 'xs':
      sizeStyles = 'px-2.5 py-1.5 text-xs';
      break;
    case 'sm':
      sizeStyles = 'px-3 py-2 text-sm leading-4';
      break;
    case 'md':
      sizeStyles = 'px-4 py-2 text-sm';
      break;
    case 'lg':
      sizeStyles = 'px-4 py-2 text-base';
      break;
    default: // Fallback to md
      sizeStyles = 'px-4 py-2 text-sm';
  }

  return (
    <button
      type="button" // Default to button type, can be overridden by props
      className={`${baseStyles} ${variantStyles} ${sizeStyles} ${className || ''}`}
      {...props}
    >
      {children}
    </button>
  );
};

export default Button;
