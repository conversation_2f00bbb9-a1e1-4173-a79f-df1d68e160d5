
import React from 'react';
import { APP_NAME } from '../constants';
import { HeartIcon } from './icons/HeartIcon'; // Ensured relative path

const Navbar: React.FC = () => {
  return (
    <nav className="bg-primary shadow-md">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <HeartIcon className="h-8 w-8 text-white mr-2" />
            <span className="font-bold text-2xl text-white">{APP_NAME}</span>
          </div>
          <div className="flex items-center">
            {/* Placeholder for user profile/settings */}
            <img
              className="h-8 w-8 rounded-full"
              src="https://picsum.photos/100/100" 
              alt="User Avatar"
            />
             <span className="ml-2 text-white font-medium hidden sm:block"><PERSON></span>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;